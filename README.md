# Kahraba - Customer Database Query System

A Next.js application that uses AI to query PostgreSQL customer utility data and extract national IDs for display in an embedded dashboard.

## Features

- **Natural Language Queries**: Ask questions in plain English about customer data
- **AI-Powered SQL Generation**: Uses Gemini AI to convert natural language to SQL queries
- **Database Integration**: Connects to PostgreSQL database containing utility customer data
- **National ID Extraction**: Automatically extracts and displays national IDs in an embedded iframe
- **Real-time Chat Interface**: Interactive chat interface for querying customer data
- **Error Handling**: Comprehensive error handling with helpful user feedback

## Architecture

1. **User Input**: User asks a question in natural language (e.g., "Find customer John Smith")
2. **SQL Generation**: Gemini AI generates a safe SQL SELECT query based on the input
3. **Database Query**: The generated SQL is executed against the PostgreSQL database
4. **Data Analysis**: Another AI agent analyzes the results and extracts national IDs
5. **Iframe Update**: The extracted national IDs are passed to an embedded dashboard
6. **Narrative Response**: A helpful response is generated explaining what was found

## Setup Instructions

### Prerequisites

- Node.js 18+
- PostgreSQL database with customer data
- Gemini AI API key

### 1. <PERSON><PERSON> and Install

```bash
git clone <repository-url>
cd kahraba
npm install
```

### 2. Database Setup

Ensure your PostgreSQL database has a table named `public.data` with the following structure:

```sql
CREATE TABLE public.data (
    "ObjectID" BIGINT PRIMARY KEY,
    nationalno TEXT,
    acivno TEXT,
    acivoff TEXT,
    afamno TEXT,
    name1 TEXT,
    name2 TEXT,
    name3 TEXT,
    name4 TEXT,
    limitmeter NUMERIC,
    birthdate DATE,
    "userType" TEXT,
    "delete" BOOLEAN,
    meterno TEXT,
    customerno TEXT,
    "Customers_name" TEXT,
    "Meter_Number" TEXT,
    "File_num" TEXT,
    statuscitizen TEXT,
    company_id BIGINT,
    company_name TEXT,
    governorate_code BIGINT,
    governorate TEXT,
    general_code BIGINT,
    "general" TEXT,
    town_code BIGINT,
    town TEXT,
    customername TEXT,
    village_code BIGINT,
    village TEXT,
    "Family_Nat_NO" TEXT,
    "First_Name" TEXT,
    "Second_Name" TEXT,
    "Third_Name" TEXT,
    "Family_Name" TEXT,
    Gender TEXT,
    "Civil_Reg_Office_Code" TEXT,
    "Civil_Registration_Number" TEXT,
    LONGTITUDE NUMERIC(12,8),
    LATITUDE NUMERIC(12,8),
    "GO_TO_METER_LOCATION" TEXT,
    X NUMERIC(12,8),
    Y NUMERIC(12,8)
);
```

### 3. Environment Configuration

Copy the example environment file and update with your credentials:

```bash
cp .env.example .env.local
```

Edit `.env.local`:

```env
# Gemini AI API Key
NEXT_PUBLIC_GEMINI_API_KEY=your_actual_gemini_api_key

# PostgreSQL Database Configuration
DB_HOST=your_database_host
DB_PORT=5432
DB_NAME=your_database_name
DB_USER=your_database_user
DB_PASSWORD=your_database_password
```

### 4. Run the Application

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to access the application.

## Usage Examples

Try asking questions like:

- "Find customer John Smith"
- "Show me meter number 12345"
- "Search for national ID 1234567890"
- "Find customers in Amman"
- "Show me all customers with family name Ahmad"
- "Find meters in the village of Zarqa"

## API Endpoints

### POST /api/query

Processes natural language queries and returns database results.

**Request:**

```json
{
  "userInput": "Find customer John Smith",
  "history": []
}
```

**Response:**

```json
{
  "success": true,
  "sql": "SELECT * FROM public.data WHERE ...",
  "explanation": "Query explanation",
  "results": [...],
  "count": 5
}
```

### GET /api/query

Health check endpoint that verifies database connectivity.

## Security Features

- SQL injection prevention through query validation
- Only SELECT queries are allowed
- Dangerous SQL keywords are blocked
- Automatic query limits to prevent large result sets
- Input sanitization and validation

## Error Handling

The application provides helpful error messages for common issues:

- Database connection failures
- Invalid SQL generation
- Network connectivity issues
- Query timeout errors

## Development

### Project Structure

```
src/
├── app/
│   ├── api/query/          # Database query API endpoint
│   ├── components/         # React components
│   └── page.tsx           # Main application page
├── lib/
│   ├── database.ts        # Database connection and utilities
│   ├── sql-agent.ts       # AI-powered SQL generation
│   ├── gemini.ts          # National ID extraction agent
│   └── types.ts           # TypeScript type definitions
```

### Building for Production

```bash
npm run build
npm start
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

[Add your license information here]
