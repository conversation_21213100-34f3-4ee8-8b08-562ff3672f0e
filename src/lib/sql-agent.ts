import OpenAI from "openai";
import { DATABASE_SCHEMA } from "./database";
import { Message } from "./types";

// Initialize the OpenAI API
const API_KEY = process.env.NEXT_PUBLIC_OPENAI_API_KEY || "YOUR_API_KEY";
const openai = new OpenAI({
  apiKey: API_KEY,
  dangerouslyAllowBrowser: true, // Only for client-side usage
});
const MODEL_NAME = "gpt-4o";

/**
 * Generate SQL query from user input using Gemini AI
 * @param userInput The user's natural language query
 * @param history Optional chat history for context
 * @returns Generated SQL query and explanation
 */
export async function generateSQLQuery(
  userInput: string,
  history: Message[] = []
): Promise<{
  sql: string;
  explanation: string;
  error?: string;
}> {
  try {
    // Preprocess Arabic commands - remove command words that are not names
    const arabicCommands = [
      "فرجيني",
      "أرني",
      "اعرض",
      "أظهر",
      "ابحث",
      "عن",
      "دور",
      "على",
      "لاقي",
      "العميل",
      "الزبون",
      "المواطن",
      "الشخص",
    ];

    let processedInput = userInput;
    arabicCommands.forEach((command) => {
      // Remove command words (case insensitive) - use global replace
      const regex = new RegExp(command, "gi");
      processedInput = processedInput.replace(regex, "").trim();
    });

    // Clean up extra spaces
    processedInput = processedInput.replace(/\s+/g, " ").trim();

    console.log("Original input:", userInput);
    console.log("Processed input (names only):", processedInput);

    if (!processedInput) {
      return {
        sql: "",
        explanation: "",
        error: "No names found in the input after removing command words",
      };
    }
    // Prepare the database schema information
    const schemaInfo = `
Database Table: ${DATABASE_SCHEMA.table_name}
Description: ${DATABASE_SCHEMA.description}

Columns:
${DATABASE_SCHEMA.columns
  .map((col) => `- ${col.name} (${col.type}): ${col.description}`)
  .join("\n")}

Search Tips:
${DATABASE_SCHEMA.search_tips.map((tip) => `- ${tip}`).join("\n")}
`;

    const systemInstruction = `CRITICAL FIRST STEP: UNDERSTAND ARABIC COMMANDS
- "فرجيني" means "SHOW ME" - it is NOT a person's name!
- "أرني" means "SHOW ME" - it is NOT a person's name!
- "ابحث عن" means "SEARCH FOR" - these are NOT names!

You are a SQL query generator for a PostgreSQL database containing utility customer data.

${schemaInfo}

CRITICAL: ARABIC COMMAND UNDERSTANDING:
- "فرجيني" / "أرني" / "اعرض" / "أظهر" = "Show me" / "Find" / "Display" - THESE ARE COMMANDS, NOT NAMES!
- "ابحث عن" / "دور على" / "لاقي" = "Search for" / "Find" - THESE ARE COMMANDS, NOT NAMES!
- "العميل" / "الزبون" = "customer"
- "رقم العداد" = "meter number"
- "الرقم الوطني" = "national ID"
- "في" = "in" (location)

STEP 1: IDENTIFY COMMANDS vs NAMES
- IGNORE command words: فرجيني, أرني, اعرض, أظهر, ابحث, عن, دور, على, لاقي, العميل, الزبون
- EXTRACT ONLY the actual person names from the input

NAME SEARCH STRATEGY:
1. Extract all name parts from the user input (IGNORE command words like فرجيني, ابحث, etc.)
2. Be very flexible with name matching:
   - If user gives 2 names: search First_Name + Family_Name OR First_Name + Second_Name OR Second_Name + Family_Name
   - If user gives 3 names: search all combinations of First_Name, Second_Name, Third_Name, Family_Name
   - If user gives 4+ names: search all possible combinations
   - Also search for partial matches in case names are compound or split differently
3. Use OR conditions to find any possible match
4. Handle Arabic names that might be written differently or have compound parts

IMPORTANT RULES:
1. ONLY generate SELECT queries - no INSERT, UPDATE, DELETE, or DDL statements
2. Always include: WHERE ("delete" IS NOT TRUE OR "delete" IS NULL) to exclude deleted records
3. Use ILIKE for case-insensitive text searches
4. Limit results to reasonable numbers (use LIMIT clause, max 100 records)
5. CRITICAL: Use exact column names with proper quotes as shown in the schema
6. When searching names, use these EXACT column names with quotes:
   - "First_Name", "Second_Name", "Third_Name", "Family_Name" (quotes required)
7. For national IDs, search in: nationalno, "Family_Nat_NO", "Civil_Registration_Number"
8. For meter numbers, search in: meterno, "Meter_Number"
9. Use proper SQL syntax for PostgreSQL
10. Column names with capital letters or underscores MUST be quoted with double quotes

EXAMPLE of flexible name search:
User input: "فرجيني أحمد محمد علي"
STEP 1: Remove command word "فرجيني" (means "show me")
STEP 2: Extract names: ["أحمد", "محمد", "علي"]
STEP 3: Generate flexible search like:
WHERE (
  ("First_Name" ILIKE '%أحمد%' AND "Family_Name" ILIKE '%محمد%') OR
  ("First_Name" ILIKE '%أحمد%' AND "Family_Name" ILIKE '%علي%') OR
  ("First_Name" ILIKE '%أحمد%' AND "Second_Name" ILIKE '%محمد%') OR
  ("Second_Name" ILIKE '%محمد%' AND "Family_Name" ILIKE '%علي%') OR
  ("First_Name" ILIKE '%أحمد%' AND "Second_Name" ILIKE '%محمد%' AND "Third_Name" ILIKE '%علي%')
  -- Add more combinations as needed
) AND ("delete" IS NOT TRUE OR "delete" IS NULL)

PROCESSED INPUT (command words removed): "${processedInput}"

Generate a flexible SQL query to search for people with these names. Be very flexible with name combinations.

The user input was in Arabic and has been preprocessed to remove command words. Generate a SQL query that would help find the customer records for the names provided.

Respond with a JSON object in this format:
{
  "sql": "SELECT ... FROM public.data WHERE ...",
  "explanation": "Brief explanation in Arabic of what the query does and why these columns/conditions were chosen"
}`;

    // Format chat history for OpenAI
    const formattedHistory = history.map((msg) => ({
      role: msg.role === "user" ? "user" : "assistant",
      content: msg.content,
    }));

    // Convert chat history to OpenAI format
    const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
      {
        role: "system",
        content: systemInstruction,
      },
    ];

    // Add chat history
    for (const msg of formattedHistory) {
      messages.push({
        role: msg.role === "user" ? "user" : "assistant",
        content: msg.content,
      });
    }

    // Add current user message
    messages.push({
      role: "user",
      content: `Search for customer with names: ${processedInput}`,
    });

    // Generate SQL query using OpenAI
    console.log("Calling OpenAI API with model:", MODEL_NAME);
    console.log("Messages:", JSON.stringify(messages, null, 2));

    const response = await openai.chat.completions.create({
      model: MODEL_NAME,
      messages: messages,
      temperature: 0.3,
      response_format: { type: "json_object" },
    });

    console.log("OpenAI API response:", response);
    const text = response.choices[0]?.message?.content || "";
    console.log("SQL Agent response:", text);

    if (!text) {
      return {
        sql: "",
        explanation: "",
        error: "No response from AI model",
      };
    }

    // Parse the JSON response
    try {
      // Remove markdown code blocks if present
      let cleanText = text.trim();
      console.log("Original text:", text);

      if (cleanText.startsWith("```json")) {
        cleanText = cleanText
          .replace(/^```json\s*/m, "")
          .replace(/\s*```\s*$/m, "");
        console.log("Removed ```json blocks");
      } else if (cleanText.startsWith("```")) {
        cleanText = cleanText
          .replace(/^```\s*/m, "")
          .replace(/\s*```\s*$/m, "");
        console.log("Removed ``` blocks");
      }

      // Additional cleanup - remove any remaining backticks or extra whitespace
      cleanText = cleanText.replace(/^`+|`+$/g, "").trim();

      console.log("Clean text:", cleanText);
      const parsed = JSON.parse(cleanText);
      console.log("parsed", parsed);
      // Validate that we have a SQL query
      if (!parsed.sql || typeof parsed.sql !== "string") {
        return {
          sql: "",
          explanation: "",
          error: "Invalid SQL query generated",
        };
      }

      // Basic SQL injection prevention - ensure it's a SELECT query
      const sqlTrimmed = parsed.sql.trim().toUpperCase();
      console.log("SQL trimmed:", sqlTrimmed, sqlTrimmed.startsWith("SELECT"));
      if (!sqlTrimmed.startsWith("SELECT")) {
        return {
          sql: "",
          explanation: "",
          error: "Only SELECT queries are allowed",
        };
      }
      console.log("after select");
      // Check for dangerous keywords (but exclude quoted column names)
      const dangerousKeywords = [
        "DROP",
        "INSERT",
        "UPDATE",
        "ALTER",
        "CREATE",
        "TRUNCATE",
        "EXEC",
        "EXECUTE",
      ];

      // Check for DELETE command but not "delete" column name
      if (sqlTrimmed.includes(" DELETE ") || sqlTrimmed.startsWith("DELETE ")) {
        console.log("Dangerous keyword detected: DELETE command");
        return {
          sql: "",
          explanation: "",
          error: `Dangerous SQL keyword detected: DELETE`,
        };
      }

      for (const keyword of dangerousKeywords) {
        // Use word boundaries to avoid matching column names
        const keywordRegex = new RegExp(`\\b${keyword}\\b`, "i");
        if (keywordRegex.test(sqlTrimmed)) {
          console.log("Dangerous keyword detected:", keyword);
          return {
            sql: "",
            explanation: "",
            error: `Dangerous SQL keyword detected: ${keyword}`,
          };
        }
      }

      console.log("SQL query generated successfully");

      // Ensure the query has a reasonable LIMIT
      if (!sqlTrimmed.includes("LIMIT")) {
        // Add a default limit if none specified
        parsed.sql = parsed.sql.trim();
        if (!parsed.sql.endsWith(";")) {
          parsed.sql += " LIMIT 100";
        } else {
          parsed.sql = parsed.sql.slice(0, -1) + " LIMIT 100;";
        }
      }
      console.log("gor here");
      return {
        sql: parsed.sql,
        explanation: parsed.explanation || "SQL query generated successfully",
      };
    } catch (parseError) {
      console.error("Error parsing SQL agent response:", parseError);
      return {
        sql: "",
        explanation: "",
        error: "Failed to parse AI response",
      };
    }
  } catch (error) {
    console.error("Error calling SQL generation API:", error);
    return {
      sql: "",
      explanation: "",
      error: "Failed to generate SQL query",
    };
  }
}

/**
 * Validate a SQL query for basic safety
 * @param sql The SQL query to validate
 * @returns Whether the query is safe to execute
 */
export function validateSQLQuery(sql: string): {
  isValid: boolean;
  error?: string;
} {
  const sqlTrimmed = sql.trim().toUpperCase();

  // Must be a SELECT query
  if (!sqlTrimmed.startsWith("SELECT")) {
    return { isValid: false, error: "Only SELECT queries are allowed" };
  }

  // Check for dangerous keywords (but exclude quoted column names)
  const dangerousKeywords = [
    "DROP",
    "INSERT",
    "UPDATE",
    "ALTER",
    "CREATE",
    "TRUNCATE",
    ";--",
    "/*",
    "*/",
  ];

  // Check for DELETE command but not "delete" column name
  if (sqlTrimmed.includes(" DELETE ") || sqlTrimmed.startsWith("DELETE ")) {
    return {
      isValid: false,
      error: `Dangerous SQL keyword detected: DELETE`,
    };
  }

  for (const keyword of dangerousKeywords) {
    // Use word boundaries for most keywords, but keep simple includes for SQL injection patterns
    if (keyword === ";--" || keyword === "/*" || keyword === "*/") {
      if (sqlTrimmed.includes(keyword)) {
        return {
          isValid: false,
          error: `Dangerous SQL keyword detected: ${keyword}`,
        };
      }
    } else {
      // Use word boundaries to avoid matching column names
      const keywordRegex = new RegExp(`\\b${keyword}\\b`, "i");
      if (keywordRegex.test(sqlTrimmed)) {
        return {
          isValid: false,
          error: `Dangerous SQL keyword detected: ${keyword}`,
        };
      }
    }
  }

  // Must reference the correct table
  if (!sqlTrimmed.includes("PUBLIC.DATA") && !sqlTrimmed.includes("DATA")) {
    return { isValid: false, error: "Query must reference the data table" };
  }

  return { isValid: true };
}
