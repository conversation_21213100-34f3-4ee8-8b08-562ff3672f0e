import OpenAI from "openai";
import { Message } from "@/lib/types";

// Initialize the OpenAI API with your API key
// In a production environment, this should be stored in an environment variable
const API_KEY = process.env.NEXT_PUBLIC_OPENAI_API_KEY || "YOUR_API_KEY";
const openai = new OpenAI({
  apiKey: API_KEY,
  dangerouslyAllowBrowser: true, // Only for client-side usage
});

// Define the model to use
const MODEL_NAME = "gpt-4o";

/**
 * Extract national IDs and generate narrative from database query results using OpenAI
 * @param message The user's message text
 * @param data Database query results
 * @param history Optional chat history for context
 * @returns An object containing extracted national IDs and narrative response
 */
export async function extractWithGemini(
  message: string,
  data: unknown,
  history: Message[] = []
): Promise<{ narrative: string; national_ids: string }> {
  try {
    const dataContext = JSON.stringify(data, null, 2);
    const systemInstruction = `
You are an assistant that analyzes database query results and extracts national IDs from customer utility data.

Database Query Results: ${dataContext}

User message: "${message}"

Your task:
1. Analyze the database results to find customer records that match the user's query
2. Extract national IDs from relevant fields (nationalno, Family_Nat_NO, Civil_Registration_Number)
3. Generate a helpful narrative response

IMPORTANT RULES:
- If you find 1-5 people, return their national IDs separated by commas (e.g: "679897897,76523645")
- If you find more than 5 people, return an empty string in "national_ids" and explain in the narrative
- If no relevant records found, return empty string in "national_ids" and explain why
- Always provide a helpful narrative in markdown format IN ARABIC
- Include relevant details like names, locations, meter numbers when available
- Consider the chat history for context
- The user input will be in Arabic, so respond in Arabic

National ID Fields to Check:
- nationalno: Primary national ID field (for main person, head of the family)
- Family_Nat_NO: Family national number (for each person in the family)
- Civil_Registration_Number: Civil registration number

Respond with a JSON object in this format:
{
  "national_ids": "comma-separated national IDs or empty string",
  "narrative": "helpful response in markdown format explaining what was found - IN ARABIC"
}

          Only respond with the JSON object, nothing else.
          `;

    // Format chat history for OpenAI
    const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
      {
        role: "system",
        content: systemInstruction,
      },
    ];

    // Add chat history
    for (const msg of history) {
      messages.push({
        role: msg.role === "user" ? "user" : "assistant",
        content: msg.content,
      });
    }

    // Add current user message
    messages.push({
      role: "user",
      content: message,
    });

    console.log("OpenAI messages:", messages);

    // Generate content using OpenAI API with chat history
    const response = await openai.chat.completions.create({
      model: MODEL_NAME,
      messages: messages,
      temperature: 0.3,
      response_format: { type: "json_object" },
    });

    console.log("OpenAI response:", response);

    const text = response.choices[0]?.message?.content || "";

    if (!text)
      return {
        national_ids: "",
        narrative: "",
      };

    // Parse the JSON response
    try {
      // Remove markdown code blocks if present
      let cleanText = text.trim();
      console.log("Gemini original text:", text);

      if (cleanText.startsWith("```json")) {
        cleanText = cleanText
          .replace(/^```json\s*/m, "")
          .replace(/\s*```\s*$/m, "");
        console.log("Removed ```json blocks");
      } else if (cleanText.startsWith("```")) {
        cleanText = cleanText
          .replace(/^```\s*/m, "")
          .replace(/\s*```\s*$/m, "");
        console.log("Removed ``` blocks");
      }

      // Additional cleanup - remove any remaining backticks or extra whitespace
      cleanText = cleanText.replace(/^`+|`+$/g, "").trim();

      console.log("Gemini clean text:", cleanText);
      const parsed = JSON.parse(cleanText);
      return parsed;
    } catch (error) {
      console.error("Error parsing Gemini response:", error);
    }

    return {
      national_ids: "",
      narrative: "",
    };
  } catch (error) {
    console.error("Error calling Gemini API:", error);
    // Fallback to basic extraction if Gemini API call fails
    return {
      national_ids: "",
      narrative: "",
    };
  }
}
