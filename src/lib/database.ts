import { Pool } from "pg";

// Database configuration
const dbConfig = {
  connectionString: process.env.DATABASE_URL,
  // Connection pool settings
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
};

// Create a connection pool
let pool: Pool | null = null;

export function getPool(): Pool {
  console.log("Getting pool", dbConfig);
  if (!pool) {
    pool = new Pool(dbConfig);

    // Handle pool errors
    pool.on("error", (err) => {
      console.error("Unexpected error on idle client", err);
    });
  }

  return pool;
}

// Execute a query with automatic connection management
export async function executeQuery<T = Record<string, unknown>>(
  text: string,
  params?: unknown[]
): Promise<T[]> {
  const pool = getPool();
  const client = await pool.connect();

  try {
    const result = await client.query(text, params);
    return result.rows;
  } catch (error) {
    console.error("Database query error:", error);
    throw error;
  } finally {
    client.release();
  }
}

// Execute a query and return the first row
export async function executeQuerySingle<T = Record<string, unknown>>(
  text: string,
  params?: unknown[]
): Promise<T | null> {
  const rows = await executeQuery<T>(text, params);
  return rows.length > 0 ? rows[0] : null;
}

// Test database connection
export async function testConnection(): Promise<boolean> {
  try {
    const pool = getPool();
    const client = await pool.connect();
    await client.query("SELECT 1");
    client.release();
    console.log("Database connection successful");
    return true;
  } catch (error) {
    console.error("Database connection failed:", error);
    return false;
  }
}

// Close the connection pool (useful for cleanup)
export async function closePool(): Promise<void> {
  if (pool) {
    await pool.end();
    pool = null;
  }
}

// Database schema information for the AI agent
export const DATABASE_SCHEMA = {
  table_name: "public.data",
  description:
    "Customer utility data table containing customer information, meter details, and location data",
  columns: [
    { name: "ObjectID", type: "BIGINT", description: "Primary key identifier" },
    {
      name: "nationalno",
      type: "TEXT",
      description: "National ID number of the customer",
    },
    { name: "acivno", type: "TEXT", description: "Civil registration number" },
    { name: "acivoff", type: "TEXT", description: "Civil office code" },
    { name: "afamno", type: "TEXT", description: "Family number" },
    {
      name: "name1",
      type: "TEXT",
      description:
        "First part of customer name (main person that has the meter, family members are registered under them)",
    },
    {
      name: "name2",
      type: "TEXT",
      description: "Second part of customer name",
    },
    { name: "name3", type: "TEXT", description: "Third part of customer name" },
    {
      name: "name4",
      type: "TEXT",
      description: "Fourth part of customer name",
    },
    { name: "limitmeter", type: "NUMERIC", description: "Meter limit value" },
    { name: "birthdate", type: "DATE", description: "Customer birth date" },
    { name: "userType", type: "TEXT", description: "Type of user/customer" },
    { name: "delete", type: "BOOLEAN", description: "Soft delete flag" },
    { name: "meterno", type: "TEXT", description: "Meter number" },
    { name: "customerno", type: "TEXT", description: "Customer number" },
    { name: "Customers_name", type: "TEXT", description: "Full customer name" },
    {
      name: "Meter_Number",
      type: "TEXT",
      description: "Alternative meter number field",
    },
    { name: "File_num", type: "TEXT", description: "File number reference" },
    { name: "statuscitizen", type: "TEXT", description: "Citizenship status" },
    { name: "company_id", type: "BIGINT", description: "Company identifier" },
    { name: "company_name", type: "TEXT", description: "Company name" },
    {
      name: "governorate_code",
      type: "BIGINT",
      description: "Governorate code",
    },
    { name: "governorate", type: "TEXT", description: "Governorate name" },
    { name: "general_code", type: "BIGINT", description: "General area code" },
    { name: "general", type: "TEXT", description: "General area name" },
    { name: "town_code", type: "BIGINT", description: "Town code" },
    { name: "town", type: "TEXT", description: "Town name" },
    { name: "customername", type: "TEXT", description: "Customer name field" },
    { name: "village_code", type: "BIGINT", description: "Village code" },
    { name: "village", type: "TEXT", description: "Village name" },
    {
      name: "Family_Nat_NO",
      type: "TEXT",
      description: "Family national number",
    },
    {
      name: "First_Name",
      type: "TEXT",
      description:
        "First name of person of the family (includes the main person, wife, kids)",
    },
    {
      name: "Second_Name",
      type: "TEXT",
      description:
        "Second name of person of the family (includes the main person, wife, kids)",
    },
    {
      name: "Third_Name",
      type: "TEXT",
      description:
        "Third name of person of the family (includes the main person, wife, kids)",
    },
    {
      name: "Family_Name",
      type: "TEXT",
      description:
        "Family name of person of the family (includes the main person, wife, kids)",
    },
    { name: "Gender", type: "TEXT", description: "Gender of the person" },
    {
      name: "Civil_Reg_Office_Code",
      type: "TEXT",
      description: "Civil registration office code",
    },
    {
      name: "Civil_Registration_Number",
      type: "TEXT",
      description: "Civil registration number",
    },
    {
      name: "LONGTITUDE",
      type: "NUMERIC(12,8)",
      description: "Longitude coordinate",
    },
    {
      name: "LATITUDE",
      type: "NUMERIC(12,8)",
      description: "Latitude coordinate",
    },
    {
      name: "GO_TO_METER_LOCATION",
      type: "TEXT",
      description: "Meter location reference",
    },
    { name: "X", type: "NUMERIC(12,8)", description: "X coordinate" },
    { name: "Y", type: "NUMERIC(12,8)", description: "Y coordinate" },
  ],
  search_tips: [
    "Use ILIKE for case-insensitive text searches (e.g., name1 ILIKE '%john%')",
    "Multiple name fields (name1, name2, name3, name4) can contain parts of the full name",
    'CRITICAL: Use quotes for mixed-case columns: "First_Name", "Second_Name", "Third_Name", "Family_Name"',
    'nationalno and "Family_Nat_NO" contain national ID numbers (note quotes on second)',
    'meterno and "Meter_Number" contain meter identifiers (note quotes on second)',
    "Location fields include governorate, town, village for geographic searches",
    'Always include WHERE "delete" IS NOT TRUE OR "delete" IS NULL to exclude deleted records',
    "Column names with capital letters or underscores MUST be quoted with double quotes",
  ],
};
