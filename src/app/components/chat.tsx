"use client";

import type React from "react";

import { useState, useRef, useEffect } from "react";
import { Send, Mic, Loader } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import ChatMessage from "./chat-message";
import { Input } from "@/components/ui/input";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Message } from "@/lib/types";
import { extractWithGemini } from "@/lib/gemini";

export default function Chat({
  setNationalIds,
}: {
  setNationalIds: React.Dispatch<React.SetStateAction<string>>;
}) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [currentAssistantMessageId, setCurrentAssistantMessageId] =
    useState("");

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleVoice = () => {
    if (typeof window === "undefined" || isListening) {
      return;
    }
    setIsListening(true);
    window
      .Speech_T_text("reflect", "en-US")
      .then((result: string) => {
        setInput(result);
        console.log("Voice input:", result);
        setIsListening(false);
        if (result.trim()) {
          const fakeEvent = { preventDefault: () => {} } as React.FormEvent;
          handleSubmit(fakeEvent);
        }
      })
      .finally(() => {
        setIsListening(false);
      });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: input,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setIsLoading(true);

    // Create a placeholder message for the assistant while we wait for Gemini
    const assistantMessageId = (Date.now() + 1).toString();
    setCurrentAssistantMessageId(assistantMessageId);
    const initialAssistantMessage: Message = {
      id: assistantMessageId,
      role: "model",
      content: "جاري البحث...",
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, initialAssistantMessage]);

    try {
      // Get all messages except the current placeholder for context
      const messageHistory = messages.filter(
        (msg) => msg.id !== assistantMessageId
      );

      // Step 1: Query the database using the new API
      const queryResponse = await fetch("/api/query", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userInput: userMessage.content,
          history: messageHistory,
        }),
      });

      const queryResult = await queryResponse.json();

      if (!queryResponse.ok) {
        throw new Error(queryResult.error || "Database query failed");
      }

      console.log("Database query result:", queryResult);

      // Step 2: Extract national IDs and generate narrative from the results
      const extractedInfo = await extractWithGemini(
        userMessage.content,
        queryResult.results,
        messageHistory
      );

      console.log("Extracted info:", extractedInfo);

      // Step 3: Update national IDs for the iframe
      if (extractedInfo.national_ids) {
        setNationalIds(extractedInfo.national_ids);
      }

      // Step 4: Update the assistant message with the narrative
      const responseContent =
        extractedInfo.narrative ||
        "وجدت بعض النتائج لكن لم أتمكن من إنشاء رد مناسب.";

      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === assistantMessageId
            ? { ...msg, content: responseContent }
            : msg
        )
      );
    } catch (e) {
      console.error("Error processing message:", e);

      let errorMessage = "عذراً، واجهت خطأ في معالجة رسالتك.";

      if (e instanceof Error) {
        if (e.message.includes("Database query failed")) {
          errorMessage =
            "لا يمكنني الاتصال بقاعدة البيانات للبحث عن سجلات العملاء. يرجى التحقق من اتصال قاعدة البيانات والمحاولة مرة أخرى.";
        } else if (e.message.includes("Failed to generate SQL")) {
          errorMessage =
            "لا يمكنني فهم طلبك بما يكفي للبحث في قاعدة البيانات. هل يمكنك إعادة صياغة سؤالك؟";
        } else if (e.message.includes("fetch")) {
          errorMessage =
            "حدث خطأ في الشبكة. يرجى التحقق من اتصالك والمحاولة مرة أخرى.";
        }
      }

      // Update the assistant message with a helpful error message
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === assistantMessageId
            ? {
                ...msg,
                content: `${errorMessage}\n\n**نصيحة:** جرب طرح أسئلة مثل:\n- "ابحث عن العميل أحمد محمد"\n- "أظهر لي رقم العداد 12345"\n- "ابحث عن الرقم الوطني 1234567890"\n- "ابحث عن العملاء في عمان"`,
              }
            : msg
        )
      );
    } finally {
      setIsLoading(false);
      setCurrentAssistantMessageId("");
      scrollToBottom();
    }
  };

  return (
    <div className="flex flex-col h-screen max-h-screen bg-gray-50 dark:bg-gray-900">
      <main className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-500">
            <p className="text-right">ابدأ محادثة مع المساعد الذكي</p>
          </div>
        ) : (
          messages.map((message) => (
            <ChatMessage
              key={message.id}
              message={message}
              loading={message.id === currentAssistantMessageId && isLoading}
            />
          ))
        )}
        <div ref={messagesEndRef} />
      </main>

      <footer className="border-t p-4 bg-white dark:bg-gray-800">
        <form onSubmit={handleSubmit} className="flex gap-2">
          <div className="relative flex-1">
            <Input
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="اكتب رسالتك هنا..."
              disabled={isLoading}
              className="pr-10 text-right"
              dir="rtl"
            />
            {!input.trim() && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type="button"
                      onClick={handleVoice}
                      disabled={isListening}
                      size="icon"
                      variant="ghost"
                      className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                    >
                      {isListening ? (
                        <Loader className="h-4 w-4 animate-spin" />
                      ) : (
                        <>
                          <Mic className="h-4 w-4" />
                          <span className="sr-only">استخدم الميكروفون</span>
                        </>
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top">استخدم الميكروفون</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
          <Button type="submit" disabled={isLoading}>
            <Send className="h-4 w-4" />
          </Button>
        </form>
      </footer>
    </div>
  );
}
