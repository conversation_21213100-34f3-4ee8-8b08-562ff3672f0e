import { cn } from "@/lib/utils";
import Markdown from "react-markdown";
import { Loader2 } from "lucide-react";
import remarkGfm from "remark-gfm";

interface ChatMessageProps {
  message: {
    role: "user" | "model";
    content: string;
  };
  loading: boolean;
}

export default function ChatMessage({ message, loading }: ChatMessageProps) {
  const isUser = message.role === "user";

  return (
    <div className={cn("flex", isUser ? "justify-start" : "justify-end")}>
      <div
        className={cn(
          "max-w-full rounded-lg p-4 text-right",
          isUser ? "bg-primary text-primary-foreground" : "bg-muted"
        )}
        dir="rtl"
      >
        {loading && (
          <div className="flex items-center justify-center gap-2 py-2">
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
            <span className="text-sm font-medium text-muted-foreground">
              جاري التفكير...
            </span>
          </div>
        )}
        {!loading && (
          <Markdown remarkPlugins={[remarkGfm]}>{message.content}</Markdown>
        )}
      </div>
    </div>
  );
}
