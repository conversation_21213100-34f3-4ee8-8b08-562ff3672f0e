"use client";
import { useEffect, useState } from "react";
import Chat from "./components/chat";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";

export default function Home() {
  const [nationalIds, setNationalIds] = useState("");
  const [url, setUrl] = useState(
    "https://infomapapp.com/ksaportal/apps/dashboards/3559823c1abc46f6a6f8d5e6bf4a7fbc#nationalid="
  );
  const [iframeOpacity, setIframeOpacity] = useState(1);

  useEffect(() => {
    // Start transition
    setIframeOpacity(0.85);

    // Update URL with national IDs
    const baseUrl =
      "https://infomapapp.com/ksaportal/apps/dashboards/3559823c1abc46f6a6f8d5e6bf4a7fbc";
    const hash = nationalIds ? `#nationalid=${nationalIds}` : "#nationalid=";
    setUrl(`${baseUrl}${hash}`);

    // Longer delay to restore opacity after content has likely loaded
    const opacityTimer = setTimeout(() => {
      setIframeOpacity(1);
    }, 100);

    return () => {
      clearTimeout(opacityTimer);
    };
  }, [nationalIds]);

  return (
    <ResizablePanelGroup
      direction="horizontal"
      className="bg-gray-900 dark:bg-gray-900"
    >
      <ResizablePanel defaultSize={30}>
        <Chat setNationalIds={setNationalIds} />
      </ResizablePanel>
      <ResizableHandle withHandle />
      <ResizablePanel defaultSize={70}>
        <div
          className="h-screen w-full relative"
          style={{
            backgroundColor: "#000000", // Light background that shows during transition
          }}
        >
          <iframe
            key={url}
            className="h-full w-full absolute top-0 left-0"
            width={"100%"}
            height={"100%"}
            src={url}
            style={{
              opacity: iframeOpacity,
              transition: "opacity 0.2s ease-in-out",
            }}
          ></iframe>
        </div>
      </ResizablePanel>
    </ResizablePanelGroup>
  );
}
