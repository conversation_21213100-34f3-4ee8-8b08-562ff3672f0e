import { NextRequest, NextResponse } from 'next/server';
import { generateSQLQuery, validateSQLQuery } from '@/lib/sql-agent';
import { executeQuery, testConnection } from '@/lib/database';
import { Message } from '@/lib/types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userInput, history = [] } = body;

    if (!userInput || typeof userInput !== 'string') {
      return NextResponse.json(
        { error: 'User input is required' },
        { status: 400 }
      );
    }

    // Test database connection first
    const isConnected = await testConnection();
    if (!isConnected) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }

    // Generate SQL query using AI
    const sqlResult = await generateSQLQuery(userInput, history as Message[]);
    
    if (sqlResult.error || !sqlResult.sql) {
      return NextResponse.json(
        { 
          error: sqlResult.error || 'Failed to generate SQL query',
          explanation: sqlResult.explanation 
        },
        { status: 400 }
      );
    }

    // Validate the generated SQL
    const validation = validateSQLQuery(sqlResult.sql);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          error: validation.error || 'Invalid SQL query',
          sql: sqlResult.sql,
          explanation: sqlResult.explanation 
        },
        { status: 400 }
      );
    }

    // Execute the query
    try {
      console.log('Executing SQL:', sqlResult.sql);
      const queryResults = await executeQuery(sqlResult.sql);
      
      return NextResponse.json({
        success: true,
        sql: sqlResult.sql,
        explanation: sqlResult.explanation,
        results: queryResults,
        count: queryResults.length
      });
    } catch (dbError) {
      console.error('Database execution error:', dbError);
      return NextResponse.json(
        { 
          error: 'Database query execution failed',
          sql: sqlResult.sql,
          explanation: sqlResult.explanation,
          dbError: dbError instanceof Error ? dbError.message : 'Unknown database error'
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Health check endpoint
export async function GET() {
  try {
    const isConnected = await testConnection();
    return NextResponse.json({
      status: 'ok',
      database: isConnected ? 'connected' : 'disconnected',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json(
      { 
        status: 'error',
        database: 'disconnected',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
